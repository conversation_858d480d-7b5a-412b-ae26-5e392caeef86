#include "system_selftest.h"
#include "fatfs.h"
#include "ff.h"
#include <string.h>
#include <stdlib.h>

/**
 * @file    system_selftest.c
 * @brief   系统自检模块实现
 * @details 提供Flash存储器、TF卡、RTC等硬件设备的自检功能
 *          采用低耦合设计，重用现有驱动接口
 * <AUTHOR> Studio
 * @date    2025-01-15
 */

// 静态函数声明
static uint32_t get_tfcard_size_kb(void);

/**
 * @brief Flash存储器独立检测函数
 * @details 通过读取Flash ID来判断Flash存储器是否正常工作
 *          检测逻辑：读取到有效的非全0或全1的ID值则认为正常
 * @param  None
 * @retval selftest_result_t 检测结果（SELFTEST_OK: 正常, SELFTEST_ERROR: 异常）
 */
selftest_result_t flash_selftest(void)
{
    uint32_t flash_id;

    // 确保SPI Flash已初始化
    spi_flash_init();

    // 读取Flash ID
    flash_id = spi_flash_read_id();

    // 检查ID有效性：有效的Flash ID不应该是全0或全1
    if (flash_id != 0 && flash_id != 0xFFFFFF && flash_id != 0xFFFFFFFF)
    {
        return SELFTEST_OK;
    }

    return SELFTEST_ERROR;
}

/**
 * @brief TF卡独立检测函数
 * @details 通过检测TF卡物理存在性和文件系统挂载来判断TF卡状态
 *          检测逻辑：物理检测 + FATFS挂载测试
 * @param  None
 * @retval selftest_result_t 检测结果（SELFTEST_OK: 正常, SELFTEST_ERROR: 异常）
 */
selftest_result_t tfcard_selftest(void)
{
    FRESULT res;

    // 1. 检查TF卡物理存在性
    if (BSP_SD_IsDetected() != SD_PRESENT)
    {
        return SELFTEST_ERROR;
    }

    // 2. 尝试挂载文件系统
    res = f_mount(&SDFatFS, SDPath, 1);
    if (res != FR_OK)
    {
        return SELFTEST_ERROR;
    }

    // 3. 挂载成功，卸载文件系统
    f_mount(NULL, SDPath, 0);

    return SELFTEST_OK;
}

/**
 * @brief RTC时间显示函数
 * @details 获取并显示当前RTC时间，格式为：RTC: YYYY-MM-DD HH:MM:SS
 *          使用HAL库接口获取时间和日期信息
 * @param  None
 * @retval None
 */
void rtc_display(void)
{
    // 函数实现将在后续任务中添加
}

/**
 * @brief 系统自检主入口函数
 * @details 执行完整的系统自检流程，包括Flash检测、TF卡检测和RTC时间显示
 *          输出格式严格按照示例要求
 * @param  None
 * @retval selftest_result_t 自检结果（SELFTEST_OK: 成功, SELFTEST_ERROR: 失败）
 */
selftest_result_t system_selftest_run(void)
{
    // 函数实现将在后续任务中添加
    return SELFTEST_OK;
}

/**
 * @brief 获取TF卡容量（KB）
 * @details 内部辅助函数，用于获取TF卡的容量信息
 * @param  None
 * @retval uint32_t TF卡容量（KB），失败返回0
 */
static uint32_t get_tfcard_size_kb(void)
{
    FRESULT res;
    DWORD free_clusters, total_clusters;
    FATFS *fs;
    uint32_t total_size_kb = 0;

    // 挂载文件系统
    res = f_mount(&SDFatFS, SDPath, 1);
    if (res != FR_OK)
    {
        return 0;
    }

    // 获取磁盘空间信息
    res = f_getfree(SDPath, &free_clusters, &fs);
    if (res == FR_OK)
    {
        // 计算总容量（KB）
        // 总簇数 * 每簇扇区数 * 每扇区字节数 / 1024
        total_clusters = fs->n_fatent - 2;  // 总可用簇数
        total_size_kb = (total_clusters * fs->csize * 512) / 1024;
    }

    // 卸载文件系统
    f_mount(NULL, SDPath, 0);

    return total_size_kb;
}
